#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机房机柜位置自动生成工具 - 主应用
"""

import os
import json
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
from utils.excel_parser import ExcelParser
from utils.rack_generator import RackGenerator
from utils.export_utils import ExportUtils

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # 解析Excel文件
            parser = ExcelParser()
            data = parser.parse_excel(filepath)
            
            if 'error' in data:
                return jsonify(data), 400
            
            # 生成机柜图数据
            generator = RackGenerator()
            rack_data = generator.generate_rack_layout(data['devices'])
            
            return jsonify({
                'success': True,
                'filename': filename,
                'data': data,
                'rack_layout': rack_data
            })
        else:
            return jsonify({'error': '不支持的文件格式，请上传 .xlsx 或 .xls 文件'}), 400
            
    except Exception as e:
        return jsonify({'error': f'文件处理失败: {str(e)}'}), 500

@app.route('/download_template')
def download_template():
    """下载Excel模板"""
    try:
        template_path = ExcelParser.create_template()
        return send_file(template_path, as_attachment=True, 
                        download_name='机柜设备模板.xlsx')
    except Exception as e:
        return jsonify({'error': f'模板生成失败: {str(e)}'}), 500

@app.route('/export', methods=['POST'])
def export_data():
    """导出数据"""
    try:
        # 从请求JSON获取数据
        request_data = request.get_json()
        if not request_data:
            return jsonify({'error': '缺少请求数据'}), 400

        format_type = request_data.get('format')
        rack_data = request_data.get('rack_data')

        if not format_type:
            return jsonify({'error': '缺少导出格式参数'}), 400
        if not rack_data:
            return jsonify({'error': '缺少机柜数据'}), 400

        exporter = ExportUtils()

        if format_type == 'html':
            file_path = exporter.export_html(rack_data)
            return send_file(file_path, as_attachment=True)
        elif format_type == 'word':
            file_path = exporter.export_word(rack_data)
            return send_file(file_path, as_attachment=True)
        elif format_type == 'excel':
            file_path = exporter.export_excel(rack_data)
            return send_file(file_path, as_attachment=True)
        else:
            return jsonify({'error': '不支持的导出格式'}), 400

    except Exception as e:
        return jsonify({'error': f'导出失败: {str(e)}'}), 500

@app.errorhandler(413)
def too_large(e):
    """文件过大错误处理"""
    return jsonify({'error': '文件过大，请上传小于16MB的文件'}), 413

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)
