/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 卡片样式 */
.upload-card, .loading-card, .summary-card, .error-card {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

/* 上传区域 */
.upload-header {
    text-align: center;
    margin-bottom: 30px;
}

.upload-header h2 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.upload-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.upload-area {
    width: 100%;
    max-width: 500px;
    height: 200px;
    border: 3px dashed #bdc3c7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #2ecc71;
    background: #e8f5e8;
}

.upload-placeholder {
    text-align: center;
    color: #7f8c8d;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #27ae60;
}

.upload-hint {
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 文件信息 */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-details i {
    color: #27ae60;
    font-size: 1.5rem;
}

.file-size {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-export {
    background: #e74c3c;
    color: white;
    margin: 5px;
}

.btn-export:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

/* 加载动画 */
.loading-card {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 汇总信息 */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* 导出按钮区域 */
.export-actions {
    margin: 30px 0;
    text-align: center;
}

.export-actions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.export-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

/* 机柜容器 */
.racks-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.rack-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.rack-header {
    text-align: center;
    margin-bottom: 20px;
}

.rack-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.rack-stats {
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* 机柜可视化 */
.rack-visual {
    border: 3px solid #34495e;
    border-radius: 8px;
    background: #ecf0f1;
    margin: 0 auto 20px;
    position: relative;
    width: 250px;
    height: 420px;
    overflow: hidden;
}

.u-slot {
    height: 10px;
    border-bottom: 1px solid #bdc3c7;
    position: relative;
    display: flex;
    align-items: center;
}

.u-label {
    position: absolute;
    left: -30px;
    font-size: 8px;
    color: #7f8c8d;
    width: 25px;
    text-align: right;
    font-weight: bold;
}

.device {
    position: absolute;
    left: 2px;
    right: 2px;
    border-radius: 3px;
    color: white;
    font-size: 10px;
    text-align: center;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(255,255,255,0.3);
    overflow: hidden;
    word-break: break-all;
    line-height: 1.1;
    padding: 1px 2px;
    box-sizing: border-box;
}

/* 根据设备高度调整字体大小 */
.device.height-1 {
    font-size: 8px;
    padding: 0 1px;
}

.device.height-2 {
    font-size: 9px;
    padding: 1px 2px;
}

.device.height-3 {
    font-size: 10px;
    padding: 2px;
}

.device.height-4-plus {
    font-size: 11px;
    padding: 2px;
    flex-direction: column;
    gap: 1px;
}

.device:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* 设备列表 */
.device-list {
    margin-top: 15px;
}

.device-list h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1rem;
}

.device-item {
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 4px;
    font-size: 0.85rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-range {
    font-size: 0.75rem;
    opacity: 0.9;
}

/* 错误样式 */
.error-card {
    border-left: 4px solid #e74c3c;
}

.error-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #e74c3c;
}

.error-content {
    background: #fdf2f2;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    color: #721c24;
}

/* 提示框 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #27ae60;
}

.toast.error {
    background: #e74c3c;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 50px;
    padding: 20px;
    color: white;
    opacity: 0.8;
}

.footer a {
    color: white;
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .upload-card, .loading-card, .summary-card, .error-card {
        padding: 20px;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .racks-container {
        grid-template-columns: 1fr;
    }
    
    .export-buttons {
        flex-direction: column;
        align-items: center;
    }
}
