// 全局变量
let currentData = null;
let uploadedFile = null;

// DOM元素
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const uploadSection = document.getElementById('uploadSection');
const loadingSection = document.getElementById('loadingSection');
const resultsSection = document.getElementById('resultsSection');
const errorSection = document.getElementById('errorSection');
const errorContent = document.getElementById('errorContent');
const summaryStats = document.getElementById('summaryStats');
const racksContainer = document.getElementById('racksContainer');
const toast = document.getElementById('toast');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // 文件输入事件
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    
    // 阻止默认拖拽行为
    document.addEventListener('dragover', e => e.preventDefault());
    document.addEventListener('drop', e => e.preventDefault());
}

// 文件选择处理
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        displayFileInfo(file);
    }
}

// 拖拽处理
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (isValidFile(file)) {
            fileInput.files = files;
            displayFileInfo(file);
        } else {
            showToast('请选择有效的Excel文件 (.xlsx 或 .xls)', 'error');
        }
    }
}

// 验证文件类型
function isValidFile(file) {
    const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
    ];
    return validTypes.includes(file.type) || 
           file.name.toLowerCase().endsWith('.xlsx') || 
           file.name.toLowerCase().endsWith('.xls');
}

// 显示文件信息
function displayFileInfo(file) {
    uploadedFile = file;
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'flex';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 下载模板
async function downloadTemplate() {
    try {
        showToast('正在生成模板...', 'success');
        const response = await fetch('/download_template');
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '机柜设备模板.xlsx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showToast('模板下载成功！', 'success');
        } else {
            throw new Error('模板下载失败');
        }
    } catch (error) {
        console.error('下载模板失败:', error);
        showToast('模板下载失败，请重试', 'error');
    }
}

// 处理文件
async function processFile() {
    if (!uploadedFile) {
        showToast('请先选择文件', 'error');
        return;
    }
    
    // 显示加载状态
    showSection('loadingSection');
    
    try {
        const formData = new FormData();
        formData.append('file', uploadedFile);
        
        const response = await fetch('/upload', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentData = result;
            displayResults(result);
            showToast('文件处理成功！', 'success');
        } else {
            displayError(result);
        }
    } catch (error) {
        console.error('文件处理失败:', error);
        displayError({ error: '文件处理失败，请检查网络连接或重试' });
    }
}

// 显示结果
function displayResults(data) {
    // 显示汇总信息
    if (data.rack_layout && data.rack_layout.summary) {
        displaySummary(data.rack_layout.summary);
    }

    // 显示机柜布局
    if (data.rack_layout && data.rack_layout.racks) {
        displayRacks(data.rack_layout.racks);
    }

    showSection('resultsSection');
}

// 显示汇总信息
function displaySummary(summary) {
    const statsHtml = `
        <div class="stat-item">
            <div class="stat-number">${summary.total_racks}</div>
            <div class="stat-label">机柜总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">${summary.total_devices}</div>
            <div class="stat-label">设备总数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">${summary.u_utilization.percentage}%</div>
            <div class="stat-label">U位利用率</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">${summary.u_utilization.used}</div>
            <div class="stat-label">已用U位</div>
        </div>
    `;
    summaryStats.innerHTML = statsHtml;
}

// 显示机柜布局
function displayRacks(racks) {
    let racksHtml = '';

    for (const [rackId, rackData] of Object.entries(racks)) {
        racksHtml += createRackCard(rackData);
    }

    racksContainer.innerHTML = racksHtml;
}

// 创建机柜卡片
function createRackCard(rackData) {
    const devicesListHtml = rackData.devices.map(device =>
        `<div class="device-item" style="background-color: ${device.color};">
            <span>${device.device_name}</span>
            <span class="device-range">U${device.start_u}-U${device.end_u}</span>
        </div>`
    ).join('');

    return `
        <div class="rack-card">
            <div class="rack-header">
                <div class="rack-title">📦 ${rackData.rack_id}</div>
                <div class="rack-stats">
                    利用率: ${rackData.utilization}% |
                    设备: ${rackData.device_count}台 |
                    占用: ${rackData.occupied_u}U
                </div>
            </div>
            <div class="rack-visual">
                ${createRackVisual(rackData)}
            </div>
            <div class="device-list">
                <h4>设备清单 (${rackData.devices.length}台):</h4>
                ${devicesListHtml}
            </div>
        </div>
    `;
}

// 创建机柜可视化
function createRackVisual(rackData) {
    let visualHtml = '';

    // 从U42到U1（从上到下显示）
    for (let u = 42; u >= 1; u--) {
        const slotData = rackData.u_slots[u - 1];
        let slotContent = `<div class="u-label">U${u}</div>`;

        if (slotData && slotData.is_start) {
            // 设备开始位置
            const deviceHeight = slotData.height * 10; // 每U 10px
            const deviceName = slotData.device_name;
            const heightClass = getDeviceHeightClass(slotData.height);
            const displayText = getDeviceDisplayText(deviceName, slotData.height);

            slotContent += `
                <div class="device ${heightClass}"
                     style="background-color: ${slotData.color}; height: ${deviceHeight}px; top: 0;"
                     title="${deviceName} (U${u}-U${u + slotData.height - 1})">
                    ${displayText}
                </div>
            `;
        }

        visualHtml += `<div class="u-slot">${slotContent}</div>`;
    }

    return visualHtml;
}

// 根据设备高度获取CSS类名
function getDeviceHeightClass(height) {
    if (height === 1) return 'height-1';
    if (height === 2) return 'height-2';
    if (height === 3) return 'height-3';
    return 'height-4-plus';
}

// 根据设备高度智能显示文字
function getDeviceDisplayText(deviceName, height) {
    // 1U设备：只显示简化名称
    if (height === 1) {
        return getShortName(deviceName);
    }

    // 2U设备：显示完整名称但可能换行
    if (height === 2) {
        return deviceName.length > 8 ? getShortName(deviceName) : deviceName;
    }

    // 3U设备：显示完整名称
    if (height === 3) {
        return deviceName;
    }

    // 4U及以上：显示完整名称，可以分行显示
    if (height >= 4) {
        if (deviceName.length > 10) {
            // 尝试在合适的位置分行
            const words = deviceName.split(/([A-Z]|\d+|[\u4e00-\u9fff]+)/);
            if (words.length > 2) {
                const mid = Math.floor(words.length / 2);
                return words.slice(0, mid).join('') + '<br>' + words.slice(mid).join('');
            }
        }
        return deviceName;
    }

    return deviceName;
}

// 获取设备简化名称
function getShortName(deviceName) {
    // 移除常见后缀
    let shortName = deviceName.replace(/[设备服务器交换机路由器防火墙存储阵列]/g, '');

    // 如果还是太长，取前6个字符
    if (shortName.length > 6) {
        shortName = shortName.substring(0, 6) + '...';
    }

    return shortName || deviceName.substring(0, 4) + '...';
}

// 显示错误
function displayError(error) {
    let errorHtml = `<p><strong>错误:</strong> ${error.error}</p>`;

    if (error.details) {
        errorHtml += '<h4>详细信息:</h4><ul>';
        error.details.forEach(detail => {
            errorHtml += `<li>${detail}</li>`;
        });
        errorHtml += '</ul>';
    }

    if (error.conflicts) {
        errorHtml += '<h4>设备冲突:</h4><ul>';
        error.conflicts.forEach(conflict => {
            errorHtml += `<li>机柜 ${conflict.rack_id}: ${conflict.device1} (${conflict.device1_range}) 与 ${conflict.device2} (${conflict.device2_range}) 位置重叠</li>`;
        });
        errorHtml += '</ul>';
    }

    if (error.expected_columns) {
        errorHtml += '<h4>期望的列名:</h4><p>' + error.expected_columns.join(', ') + '</p>';
    }

    errorContent.innerHTML = errorHtml;
    showSection('errorSection');
}

// 导出数据
async function exportData(format) {
    if (!currentData) {
        showToast('没有数据可导出', 'error');
        return;
    }

    try {
        showToast(`正在生成${format.toUpperCase()}文件...`, 'success');

        const params = new URLSearchParams({
            data: JSON.stringify(currentData.rack_layout)
        });

        const response = await fetch(`/export/${format}?${params}`);

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const extensions = { html: 'html', word: 'docx', excel: 'xlsx' };
            a.download = `机柜布局_${timestamp}.${extensions[format]}`;

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showToast(`${format.toUpperCase()}文件导出成功！`, 'success');
        } else {
            const error = await response.json();
            throw new Error(error.error || '导出失败');
        }
    } catch (error) {
        console.error('导出失败:', error);
        showToast(`导出失败: ${error.message}`, 'error');
    }
}

// 重置上传
function resetUpload() {
    currentData = null;
    uploadedFile = null;
    fileInput.value = '';
    fileInfo.style.display = 'none';
    showSection('uploadSection');
}

// 显示指定区域
function showSection(sectionId) {
    const sections = ['uploadSection', 'loadingSection', 'resultsSection', 'errorSection'];
    sections.forEach(id => {
        document.getElementById(id).style.display = id === sectionId ? 'block' : 'none';
    });
}

// 显示提示消息
function showToast(message, type = 'success') {
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}
