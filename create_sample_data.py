#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例数据文件
"""

from openpyxl import Workbook
import os

def create_sample_excel():
    """创建示例Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = "机柜设备清单"
    
    # 添加表头
    headers = ['机柜编号', '设备名称', '起始U位', '高度U', '备注']
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # 添加示例数据
    sample_data = [
        ['RACK-01', '防火墙A', 1, 2, '主防火墙设备'],
        ['RACK-01', '核心交换机', 3, 1, '核心网络交换机'],
        ['RACK-01', 'Web服务器1', 5, 4, 'Apache Web服务器'],
        ['RACK-01', '数据库服务器', 10, 6, 'MySQL数据库服务器'],
        ['RACK-01', '存储设备', 17, 3, 'NAS存储设备'],
        ['RACK-01', '监控服务器', 21, 2, '系统监控服务器'],
        
        ['RACK-02', '负载均衡器', 1, 1, 'F5负载均衡器'],
        ['RACK-02', '应用服务器1', 3, 4, 'Tomcat应用服务器'],
        ['RACK-02', '应用服务器2', 8, 4, 'Tomcat应用服务器'],
        ['RACK-02', '缓存服务器', 13, 2, 'Redis缓存服务器'],
        ['RACK-02', '备份服务器', 16, 4, '数据备份服务器'],
        
        ['RACK-03', '路由器', 1, 1, '核心路由器'],
        ['RACK-03', '接入交换机1', 3, 1, '接入层交换机'],
        ['RACK-03', '接入交换机2', 5, 1, '接入层交换机'],
        ['RACK-03', 'UPS电源', 7, 2, '不间断电源'],
        ['RACK-03', '配线架', 10, 1, '网络配线架'],
    ]
    
    for row, row_data in enumerate(sample_data, 2):
        for col, value in enumerate(row_data, 1):
            ws.cell(row=row, column=col, value=value)
    
    # 保存文件
    filename = 'sample_rack_data.xlsx'
    wb.save(filename)
    print(f"示例Excel文件已创建: {filename}")
    
    return filename

if __name__ == '__main__':
    create_sample_excel()
