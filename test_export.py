#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导出功能
"""

import json
import requests

def test_export():
    """测试导出功能"""
    
    # 读取上传结果
    with open('upload_result.json', 'r', encoding='utf-8') as f:
        upload_data = json.load(f)
    
    # 提取机柜布局数据
    rack_data = upload_data['rack_layout']
    
    # 测试HTML导出
    print("测试HTML导出...")
    response = requests.post('http://localhost:8080/export', 
                           json={'format': 'html', 'rack_data': rack_data})
    
    if response.status_code == 200:
        with open('exported_rack_layout.html', 'wb') as f:
            f.write(response.content)
        print("✅ HTML导出成功: exported_rack_layout.html")
    else:
        print(f"❌ HTML导出失败: {response.text}")
    
    # 测试Word导出
    print("测试Word导出...")
    response = requests.post('http://localhost:8080/export', 
                           json={'format': 'word', 'rack_data': rack_data})
    
    if response.status_code == 200:
        with open('exported_rack_layout.docx', 'wb') as f:
            f.write(response.content)
        print("✅ Word导出成功: exported_rack_layout.docx")
    else:
        print(f"❌ Word导出失败: {response.text}")
    
    # 测试Excel导出
    print("测试Excel导出...")
    response = requests.post('http://localhost:8080/export', 
                           json={'format': 'excel', 'rack_data': rack_data})
    
    if response.status_code == 200:
        with open('exported_rack_layout.xlsx', 'wb') as f:
            f.write(response.content)
        print("✅ Excel导出成功: exported_rack_layout.xlsx")
    else:
        print(f"❌ Excel导出失败: {response.text}")

if __name__ == '__main__':
    test_export()
