#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel解析器测试
"""

import unittest
import tempfile
import os
from openpyxl import Workbook
from utils.excel_parser import ExcelParser

class TestExcelParser(unittest.TestCase):
    """Excel解析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = ExcelParser()
    
    def create_test_excel(self, data):
        """创建测试用Excel文件"""
        wb = Workbook()
        ws = wb.active
        
        # 添加表头
        headers = ['机柜编号', '设备名称', '起始U位', '高度U', '备注']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 添加数据
        for row, row_data in enumerate(data, 2):
            for col, value in enumerate(row_data, 1):
                ws.cell(row=row, column=col, value=value)
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        wb.save(temp_file.name)
        temp_file.close()
        
        return temp_file.name
    
    def test_valid_excel_parsing(self):
        """测试有效Excel文件解析"""
        test_data = [
            ['RACK-01', '防火墙A', 1, 2, '主防火墙'],
            ['RACK-01', '交换机B', 3, 1, '核心交换机'],
            ['RACK-02', '服务器C', 5, 4, 'Web服务器']
        ]
        
        excel_file = self.create_test_excel(test_data)
        
        try:
            result = self.parser.parse_excel(excel_file)
            
            self.assertTrue(result.get('success'))
            self.assertEqual(len(result['devices']), 3)
            self.assertEqual(len(result['racks']), 2)
            
            # 检查第一个设备
            device1 = result['devices'][0]
            self.assertEqual(device1['rack_id'], 'RACK-01')
            self.assertEqual(device1['device_name'], '防火墙A')
            self.assertEqual(device1['start_u'], 1)
            self.assertEqual(device1['height_u'], 2)
            self.assertEqual(device1['end_u'], 2)
            
        finally:
            os.unlink(excel_file)
    
    def test_device_conflict_detection(self):
        """测试设备冲突检测"""
        test_data = [
            ['RACK-01', '设备A', 1, 3, ''],
            ['RACK-01', '设备B', 2, 2, '']  # 与设备A冲突
        ]
        
        excel_file = self.create_test_excel(test_data)
        
        try:
            result = self.parser.parse_excel(excel_file)
            
            self.assertIn('error', result)
            self.assertIn('conflicts', result)
            self.assertEqual(len(result['conflicts']), 1)
            
        finally:
            os.unlink(excel_file)
    
    def test_invalid_u_position(self):
        """测试无效U位"""
        test_data = [
            ['RACK-01', '设备A', 0, 1, ''],  # 起始U位无效
            ['RACK-01', '设备B', 40, 5, '']  # 超出机柜范围
        ]
        
        excel_file = self.create_test_excel(test_data)
        
        try:
            result = self.parser.parse_excel(excel_file)
            
            self.assertIn('error', result)
            self.assertIn('details', result)
            
        finally:
            os.unlink(excel_file)
    
    def test_missing_columns(self):
        """测试缺少必需列"""
        wb = Workbook()
        ws = wb.active
        
        # 只添加部分列
        headers = ['机柜编号', '设备名称']  # 缺少起始U位和高度U
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        wb.save(temp_file.name)
        temp_file.close()
        
        try:
            result = self.parser.parse_excel(temp_file.name)

            self.assertIn('error', result)
            # 可能是"缺少必需的列"或"Excel文件为空"
            self.assertTrue('缺少必需的列' in result['error'] or 'Excel文件为空' in result['error'])

        finally:
            os.unlink(temp_file.name)

if __name__ == '__main__':
    unittest.main()
