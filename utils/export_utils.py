#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出工具
"""

import os
import tempfile
from datetime import datetime
from docx import Document
from docx.shared import Inches, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import xlsxwriter
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

class ExportUtils:
    """导出工具类"""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
    
    def export_html(self, data):
        """导出为HTML格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"机柜布局_{timestamp}.html"
        filepath = os.path.join(self.temp_dir, filename)
        
        html_content = self._generate_html_content(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filepath
    
    def export_word(self, data):
        """导出为Word格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"机柜布局_{timestamp}.docx"
        filepath = os.path.join(self.temp_dir, filename)
        
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('机房机柜布局报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加生成时间
        doc.add_paragraph(f'生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        
        # 添加汇总信息
        if 'summary' in data:
            self._add_summary_to_word(doc, data['summary'])
        
        # 添加每个机柜的详细信息
        if 'racks' in data:
            for rack_id, rack_data in data['racks'].items():
                self._add_rack_to_word(doc, rack_data)
        
        doc.save(filepath)
        return filepath
    
    def export_excel(self, data):
        """导出为Excel格式"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"机柜布局_{timestamp}.xlsx"
        filepath = os.path.join(self.temp_dir, filename)
        
        wb = Workbook()
        
        # 删除默认工作表
        wb.remove(wb.active)
        
        # 创建汇总工作表
        if 'summary' in data:
            self._create_summary_sheet(wb, data['summary'])
        
        # 为每个机柜创建工作表
        if 'racks' in data:
            for rack_id, rack_data in data['racks'].items():
                self._create_rack_sheet(wb, rack_data)
        
        wb.save(filepath)
        return filepath
    
    def _generate_html_content(self, data):
        """生成HTML内容"""
        html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机房机柜布局图</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .summary { background: white; padding: 20px; border-radius: 8px; margin-bottom: 30px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .rack-container { display: flex; flex-wrap: wrap; gap: 30px; justify-content: center; }
        .rack { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .rack-header { text-align: center; margin-bottom: 15px; }
        .rack-title { font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .rack-stats { font-size: 12px; color: #7f8c8d; }
        .rack-visual { border: 2px solid #34495e; width: 200px; height: 420px; position: relative; background: #ecf0f1; }
        .u-slot { height: 10px; border-bottom: 1px solid #bdc3c7; position: relative; display: flex; align-items: center; }
        .device { position: absolute; left: 0; right: 0; border-radius: 2px; color: white; font-size: 8px; 
                 text-align: center; font-weight: bold; display: flex; align-items: center; justify-content: center;
                 text-shadow: 1px 1px 1px rgba(0,0,0,0.5); }
        .u-label { position: absolute; left: -25px; font-size: 8px; color: #7f8c8d; width: 20px; text-align: right; }
        .device-list { margin-top: 15px; }
        .device-item { padding: 5px; margin: 2px 0; border-radius: 3px; font-size: 12px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .stat-label { font-size: 12px; color: #7f8c8d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 机房机柜布局图</h1>
        <p>生成时间: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """</p>
    </div>
"""
        
        # 添加汇总信息
        if 'summary' in data:
            html += self._generate_summary_html(data['summary'])
        
        # 添加机柜布局
        if 'racks' in data:
            html += '<div class="rack-container">'
            for rack_id, rack_data in data['racks'].items():
                html += self._generate_rack_html(rack_data)
            html += '</div>'
        
        html += """
</body>
</html>
"""
        return html
    
    def _generate_summary_html(self, summary):
        """生成汇总信息HTML"""
        html = '<div class="summary">'
        html += '<h2>📊 汇总信息</h2>'
        html += '<div class="stats-grid">'
        
        html += f'''
        <div class="stat-card">
            <div class="stat-number">{summary['total_racks']}</div>
            <div class="stat-label">机柜总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary['total_devices']}</div>
            <div class="stat-label">设备总数</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{summary['u_utilization']['percentage']}%</div>
            <div class="stat-label">U位利用率</div>
        </div>
        '''
        
        html += '</div></div>'
        return html
    
    def _generate_rack_html(self, rack_data):
        """生成单个机柜的HTML"""
        html = f'''
        <div class="rack">
            <div class="rack-header">
                <div class="rack-title">📦 {rack_data['rack_id']}</div>
                <div class="rack-stats">
                    利用率: {rack_data.get('utilization', 0)}% |
                    设备: {rack_data.get('device_count', 0)}台 |
                    占用: {rack_data.get('occupied_u', 0)}U
                </div>
            </div>
            <div class="rack-visual">
        '''
        
        # 生成U位
        for u in range(42, 0, -1):  # 从U42到U1（从上到下）
            slot_data = rack_data['u_slots'][u-1] if u <= len(rack_data['u_slots']) else None
            html += f'<div class="u-slot">'
            html += f'<div class="u-label">U{u}</div>'
            
            if slot_data and slot_data['is_start']:
                # 设备开始位置
                device_height = slot_data['height'] * 10  # 每U 10px
                html += f'''
                <div class="device" style="background-color: {slot_data['color']}; height: {device_height}px; top: 0;">
                    {slot_data['device_name']}
                </div>
                '''
            
            html += '</div>'
        
        html += '</div>'
        
        # 添加设备列表
        html += '<div class="device-list">'
        html += '<h4>设备清单:</h4>'
        for device in rack_data['devices']:
            html += f'''
            <div class="device-item" style="background-color: {device['color']}; color: white;">
                {device['device_name']} (U{device['start_u']}-U{device['end_u']})
            </div>
            '''
        html += '</div>'
        
        html += '</div>'
        return html
    
    def _add_summary_to_word(self, doc, summary):
        """向Word文档添加汇总信息"""
        doc.add_heading('汇总信息', level=1)
        
        # 创建表格
        table = doc.add_table(rows=4, cols=2)
        table.style = 'Table Grid'
        
        # 填充数据
        cells = table.rows[0].cells
        cells[0].text = '机柜总数'
        cells[1].text = str(summary['total_racks'])
        
        cells = table.rows[1].cells
        cells[0].text = '设备总数'
        cells[1].text = str(summary['total_devices'])
        
        cells = table.rows[2].cells
        cells[0].text = 'U位利用率'
        cells[1].text = f"{summary['u_utilization']['percentage']}%"
        
        cells = table.rows[3].cells
        cells[0].text = '机柜列表'
        cells[1].text = ', '.join(summary['rack_list'])
    
    def _add_rack_to_word(self, doc, rack_data):
        """向Word文档添加机柜信息"""
        doc.add_heading(f'机柜 {rack_data["rack_id"]}', level=2)
        
        # 机柜统计信息
        p = doc.add_paragraph()
        p.add_run(f'利用率: {rack_data.get("utilization", 0)}% | ')
        p.add_run(f'设备数量: {rack_data.get("device_count", 0)}台 | ')
        p.add_run(f'占用U位: {rack_data.get("occupied_u", 0)}U')
        
        # 设备列表表格
        if rack_data['devices']:
            table = doc.add_table(rows=1, cols=5)
            table.style = 'Table Grid'
            
            # 表头
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = '设备名称'
            hdr_cells[1].text = '起始U位'
            hdr_cells[2].text = '结束U位'
            hdr_cells[3].text = '高度U'
            hdr_cells[4].text = '备注'
            
            # 设备数据
            for device in rack_data['devices']:
                row_cells = table.add_row().cells
                row_cells[0].text = device['device_name']
                row_cells[1].text = str(device['start_u'])
                row_cells[2].text = str(device['end_u'])
                row_cells[3].text = str(device['height_u'])
                row_cells[4].text = device.get('remarks', '')
    
    def _create_summary_sheet(self, wb, summary):
        """创建汇总工作表"""
        ws = wb.create_sheet(title="汇总信息")
        
        # 设置标题
        ws['A1'] = '机房机柜汇总报告'
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:B1')
        
        # 基本信息
        ws['A3'] = '机柜总数'
        ws['B3'] = summary['total_racks']
        ws['A4'] = '设备总数'
        ws['B4'] = summary['total_devices']
        ws['A5'] = 'U位利用率'
        ws['B5'] = f"{summary['u_utilization']['percentage']}%"
        
        # 设置样式
        for row in range(3, 6):
            ws[f'A{row}'].font = Font(bold=True)
    
    def _create_rack_sheet(self, wb, rack_data):
        """创建机柜工作表"""
        ws = wb.create_sheet(title=rack_data['rack_id'])
        
        # 标题
        ws['A1'] = f'机柜 {rack_data["rack_id"]} 详细信息'
        ws['A1'].font = Font(size=14, bold=True)
        ws.merge_cells('A1:E1')
        
        # 统计信息
        ws['A3'] = '利用率'
        ws['B3'] = f"{rack_data.get('utilization', 0)}%"
        ws['C3'] = '设备数量'
        ws['D3'] = f"{rack_data.get('device_count', 0)}台"
        
        # 设备列表表头
        headers = ['设备名称', '起始U位', '结束U位', '高度U', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 设备数据
        for row, device in enumerate(rack_data['devices'], 6):
            ws.cell(row=row, column=1, value=device['device_name'])
            ws.cell(row=row, column=2, value=device['start_u'])
            ws.cell(row=row, column=3, value=device['end_u'])
            ws.cell(row=row, column=4, value=device['height_u'])
            ws.cell(row=row, column=5, value=device.get('remarks', ''))
        
        # 调整列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 10
        ws.column_dimensions['C'].width = 10
        ws.column_dimensions['D'].width = 10
        ws.column_dimensions['E'].width = 20
